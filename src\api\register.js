import fetch from 'node-fetch';
import { encryptRequest } from '../../encryption.js';
import { saveUser } from '../database/database.js';
import { generateDeviceInfo } from '../device/deviceInfo.js';

// Register a new user
export const registerUser = async (ctx, chatId, userData) => {
    const { firstname, lastname, email, phonenumber, password, transactionPin } = userData;
    
    // Validate required fields
    if (!firstname || !lastname || !email || !phonenumber || !password || !transactionPin) {
        await ctx.reply("❌ All fields are required for registration.");
        return false;
    }
    
    console.log('Registration Data:', { firstname, lastname, email, phonenumber });
    
    const requestBody = JSON.stringify({
        firstname,
        lastname,
        email,
        phonenumber,
        password,
        transactionPin
    });
    
    console.log('Registration Request:', requestBody);
    const encryptedData = encryptRequest(requestBody);
    console.log('Encrypted Registration Data:', encryptedData);

    try {
        const response = await fetch('https://api.doubledata.ng/api/v1/user/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Registration Successful! User Data:', data);
            
            await ctx.reply("✅ Registration successful! Please check your email for a verification link to activate your account.");
            
            return true;
        } else {
            await ctx.reply(`❌ Registration failed: ${data.message || 'An error occurred'}`);
            console.log('Registration Error:', data);
            return false;
        }
    } catch (error) {
        console.error('Registration Error:', error);
        await ctx.reply("⚠️ An error occurred during registration. Please try again later.");
        return false;
    }
};

// Verify user account with token
export const verifyAccount = async (ctx, emailAddress, token) => {
    try {
        const response = await fetch(`https://api.doubledata.ng/api/v1/user/verify-account/${emailAddress}/${token}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Account verification successful!', data);
            await ctx.reply("✅ Your account has been verified successfully! You can now log in.");
            return true;
        } else {
            console.log('❌ Account verification failed:', data);
            await ctx.reply(`❌ Account verification failed: ${data.message || 'Invalid token'}`);
            return false;
        }
    } catch (error) {
        console.error('Account verification error:', error);
        await ctx.reply("⚠️ An error occurred during account verification. Please try again later.");
        return false;
    }
};

// Resend account verification email
export const resendVerificationEmail = async (ctx, emailAddress) => {
    try {
        const response = await fetch(`https://api.doubledata.ng/api/v1/user/resend-verification-email/${emailAddress}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Verification email resent successfully!', data);
            await ctx.reply("✅ A new verification email has been sent. Please check your inbox and spam folder.");
            return true;
        } else {
            console.log('❌ Failed to resend verification email:', data);
            await ctx.reply(`❌ Failed to resend verification email: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Resend verification email error:', error);
        await ctx.reply("⚠️ An error occurred while resending the verification email. Please try again later.");
        return false;
    }
};

// Forgot password
export const forgotPassword = async (ctx, emailOrPhone) => {
    try {
        const response = await fetch(`https://api.doubledata.ng/api/v1/user/forgot-password/${emailOrPhone}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Password reset token sent successfully!', data);
            await ctx.reply("✅ A password reset token has been sent to your email or phone. Please check and follow the instructions.");
            return true;
        } else {
            console.log('❌ Failed to send password reset token:', data);
            await ctx.reply(`❌ Failed to send password reset token: ${data.message || 'Invalid email or phone'}`);
            return false;
        }
    } catch (error) {
        console.error('Forgot password error:', error);
        await ctx.reply("⚠️ An error occurred while processing your request. Please try again later.");
        return false;
    }
};

// Reset password
export const resetPassword = async (ctx, userId, token, newPassword, confirmPassword) => {
    if (newPassword !== confirmPassword) {
        await ctx.reply("❌ New password and confirmation do not match.");
        return false;
    }

    const requestBody = JSON.stringify({
        userId,
        token,
        newPassword,
        confirmPassword
    });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch('https://api.doubledata.ng/api/v1/user/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Password reset successful!', data);
            await ctx.reply("✅ Your password has been reset successfully! You can now log in with your new password.");
            return true;
        } else {
            console.log('❌ Password reset failed:', data);
            await ctx.reply(`❌ Password reset failed: ${data.message || 'Invalid token'}`);
            return false;
        }
    } catch (error) {
        console.error('Password reset error:', error);
        await ctx.reply("⚠️ An error occurred while resetting your password. Please try again later.");
        return false;
    }
};