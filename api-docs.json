{"openapi": "3.0.0", "info": {"title": "Double Data API Documentations", "version": "1.0.0"}, "paths": {"/api/v1/admin/login": {"patch": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Login an admin", "description": "<PERSON><PERSON>", "operationId": "99512849b7937d77e99a3895095eb827", "requestBody": {"content": {"application/json": {"schema": {"properties": {"": {"properties": {"email": {"type": "string"}, "password": {"type": "string"}}, "type": "object"}}, "type": "object", "example": {"email": "<EMAIL>", "password": "mypassword"}}}}}, "responses": {"200": {"description": "Please verify your login to proceed"}}}}, "/api/v1/admin/loginVerify": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Verify login token", "description": "<PERSON><PERSON>", "operationId": "d874850a84b35988617137c62bcdbcf9", "requestBody": {"content": {"application/json": {"schema": {"properties": {"": {"properties": {"email": {"type": "string"}, "password": {"type": "string"}, "token": {"type": "string"}}, "type": "object"}}, "type": "object", "example": {"email": "<EMAIL>", "password": "mypassword", "token": "657483"}}}}}, "responses": {"200": {"description": "<PERSON><PERSON>l"}}}}, "/api/v1/admin/create-verification-token": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Create token for admin", "description": "Create token", "operationId": "1ea158e972ced48c797aca9c10c727db", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "type", "channel"], "properties": {"userId": {"type": "integer"}, "type": {"type": "string", "enum": ["2fa", "register", "reset"]}, "channel": {"type": "string", "enum": ["sms", "email", "any"]}, "whatsapp": {"type": "boolean"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Created successfully"}}}}, "/api/v1/admin/verify-verification-token": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Verify user token", "description": "Verify token", "operationId": "6815841b6e97ae17c6a431529f9b9fba", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "type", "verifying"], "properties": {"userId": {"type": "integer"}, "token": {"type": "string", "example": "5463"}, "verifying": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Confirmed successfully"}}}}, "/api/v1/admin/check-verification-token": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Check admin token", "description": "Check token", "operationId": "db953cbd5b4ba227ce14238379df6fc8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "token", "verifying"], "properties": {"userId": {"type": "integer"}, "token": {"type": "string"}, "verifying": {"type": "string", "enum": ["2fa", "register", "reset"]}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Confirmed successfully"}}}}, "/api/v1/admin/forgot-password/{emailOrPhone}": {"get": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Forgot password", "description": "Forgot Password", "operationId": "54cf6bee6b546f60ac6fae77c0772b7b", "parameters": [{"name": "emailOrPhone", "in": "path", "description": "User's email address or phone number", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Reset token sent successfuly"}}}}, "/api/v1/admin/reset-password": {"post": {"tags": ["Admin Au<PERSON>ntication"], "summary": "Reset password", "description": "Reset Password", "operationId": "7bc948e85f4c400a4b9e0c61baf63b77", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "token", "newPassword", "confirmPassword"], "properties": {"userId": {"type": "integer"}, "token": {"type": "string"}, "newPassword": {"type": "string"}, "confirmPassword": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Confirmed successfuly"}}}}, "/api/v1/admin/staffs": {"get": {"tags": ["Admin"], "summary": "List admins", "description": "List Admin", "operationId": "e51a635094cbe8e5a7c765deeac96fb6", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Admin"], "summary": "Create admin", "description": "Create Admin", "operationId": "264fa6432e41e9709a409afe27a7eb7d", "parameters": [{"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateAdminRequest"}}}}, "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/staffs/load/simple": {"get": {"tags": ["Admin"], "summary": "List admin simple", "description": "List Admin Simple", "operationId": "fc98c47ea93a54fcc13cbd66dd421913", "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/staffs/{id}": {"get": {"tags": ["Admin"], "summary": "View admin details", "description": "View Admin", "operationId": "4a20320864f82d21d6b81c334e646907", "parameters": [{"name": "id", "in": "path", "description": "Admin ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Admin"], "summary": "Edit admin data", "description": "Edit Admin", "operationId": "29ce2177fac7d15b5bd95ab847ecdd72", "parameters": [{"name": "id", "in": "path", "description": "Admin ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminEditAdminRequest"}}}}, "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin"], "summary": "Delete admins", "description": "Delete Admin", "operationId": "04842959111d7b0725fb5c5ad8f94732", "parameters": [{"name": "id", "in": "path", "description": "Admin ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Admin"], "summary": "Change admin status", "description": "Change Admin Status", "operationId": "015fd4a08912578da5ac82b4312c1d1c", "parameters": [{"name": "id", "in": "path", "description": "Admin ID", "required": true, "schema": {"type": "integer"}}, {"name": "purpose", "in": "query", "description": "Restriction Purpose", "required": false, "schema": {"type": "string"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/api-keys": {"get": {"tags": ["Admin API Keys"], "summary": "List api keys", "description": "List API Keys", "operationId": "b445f61dc29fe32b6e3362065ba0b409", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Admin API Keys"], "summary": "Create API Key", "description": "Create API Key", "operationId": "afce2f41dd91f30bcb10579c5919d52f", "parameters": [{"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateApiKeyRequest"}}}}, "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/api-keys/{id}": {"get": {"tags": ["Admin API Keys"], "summary": "View api keys details", "description": "View Api Key", "operationId": "fddf81dd88b5d9a68a0563ccbc1dad14", "parameters": [{"name": "id", "in": "path", "description": "API Key ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Admin API Keys"], "summary": "Edit api key data", "description": "Edit API Key", "operationId": "9e59042f5270b7aaa2c4a6974d5cb5b0", "parameters": [{"name": "id", "in": "path", "description": "API Key ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateApiKeyRequest"}}}}, "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin API Keys"], "summary": "Delete api keys", "description": "Delete API Key", "operationId": "21f6f60209fbb99aed01136bf8b46a4a", "parameters": [{"name": "id", "in": "path", "description": "API Key ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/dashboard": {"get": {"tags": ["Admin"], "summary": "Load dashboard", "description": "Load Dashboard", "operationId": "2caa4fb6f83b2c8ed4a18c7f71405852", "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/devices": {"get": {"tags": ["Admin Devices"], "summary": "List devices", "description": "List Devices", "operationId": "191e8b33dd2be09fedde58185a13e18f", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/devices/{id}": {"get": {"tags": ["Admin Devices"], "summary": "View device details", "description": "View Devices", "operationId": "08b1cada52531fdd034628e68daebe9b", "parameters": [{"name": "id", "in": "path", "description": "Device ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin Devices"], "summary": "Delete device", "description": "Delete Currencies", "operationId": "be3a4f1e57a56cfa4506ed441f3c3a6b", "parameters": [{"name": "id", "in": "path", "description": "Device ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/kyc-levels": {"get": {"tags": ["Admin KYC Levels"], "summary": "List faqs", "description": "List Kyc Levels", "operationId": "e730b7e6d5015d1ddbc4b699b23726f2", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Admin KYC Levels"], "summary": "Create kyc level", "description": "Create KYC Levels", "operationId": "6f5f0f6bdbf0d55b86f87e35fbfadbc7", "parameters": [{"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateKycLevelRequest"}}}}, "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/kyc-levels/{id}": {"get": {"tags": ["Admin KYC Levels"], "summary": "View kyc level details", "description": "View Kyc Level", "operationId": "1fe1e242a7400faa697ef5c4045287cc", "parameters": [{"name": "id", "in": "path", "description": "KYC Level ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Admin KYC Levels"], "summary": "Edit kyc level data", "description": "Edit KYC Level", "operationId": "ae9f0ea6155a49c92fcab970e820441d", "parameters": [{"name": "id", "in": "path", "description": "KYC Level ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateKycLevelRequest"}}}}, "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin KYC Levels"], "summary": "Delete kyc level", "description": "Delete KYC Level", "operationId": "fa6595ae1d85d2cb9fb35b9dbfb29e5d", "parameters": [{"name": "id", "in": "path", "description": "KYC Level ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile/upload-passport": {"post": {"tags": ["Admin Profile"], "summary": "Upload Passport Photograp", "operationId": "28d647a4e93981a74da440a30c41c18f", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PassportRequest"}}}}, "responses": {"200": {"description": "Request sent"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile/change-password": {"post": {"tags": ["Admin Profile"], "summary": "Change account password", "operationId": "9e9d4d3010f961e06c3ef5138bc36809", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile/create-pin": {"post": {"tags": ["Admin Profile"], "summary": "Create account transaction pin", "operationId": "e8c82d8dbb2f41f5f74ee54133badbcd", "parameters": [{"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile/change-pin": {"get": {"tags": ["Admin Profile"], "summary": "Change account transaction pin", "operationId": "dd64da3b486f561d40510ccb9a36d07e", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePinRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile/logout": {"get": {"tags": ["Admin Profile"], "summary": "Sign out", "operationId": "998b9b6fa61467adb5e3f26f35e52028", "responses": {"200": {"description": "Request Successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile": {"get": {"tags": ["User Profile"], "summary": "Update profile address", "operationId": "1e9e3182e947ff72fd7f43fdd387d68a", "responses": {"200": {"description": "Request sent"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile/notifications": {"post": {"tags": ["Admin Profile"], "summary": "List notifications", "operationId": "e8872bd95a169a85e9a795b3bdb45d47", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/profile/notifications/{id}": {"post": {"tags": ["Admin Profile"], "summary": "Single notifications", "operationId": "327b495340bf8fd4dbc17f3f703cc274", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/roles": {"get": {"tags": ["Admin Roles"], "summary": "List Roles", "description": "List Roles", "operationId": "07ed37b28b4327f0f7832000754919a6", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/roles/load/simple": {"get": {"tags": ["Admin Roles"], "summary": "List Simple Roles", "description": "List Role Simple", "operationId": "f3fee5c9b89d166c8566f48f5f9978d5", "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/roles/{id}": {"get": {"tags": ["Admin Roles"], "summary": "View role details", "description": "View Role", "operationId": "2ef32558689bbbce8024748c230cb8e2", "parameters": [{"name": "id", "in": "path", "description": "Role ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/roles/create-role": {"post": {"tags": ["Admin Roles"], "summary": "Create new role", "description": "Create Roles", "operationId": "69458d6ae930c5379df27bf351d99ba2", "parameters": [{"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreatePermissionRoleRequest"}}}}, "responses": {"200": {"description": "Created successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/roles/edit-role/{id}": {"put": {"tags": ["Admin Roles"], "summary": "Edit roles data", "description": "Edit Role", "operationId": "bc8ce6d256b7ffba940b2ecffd3c3792", "parameters": [{"name": "id", "in": "path", "description": "Role ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreatePermissionRoleRequest"}}}}, "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/roles/delete-role/{id}": {"delete": {"tags": ["Admin Roles"], "summary": "Delete Roles", "description": "Delete Roles", "operationId": "398f08fb0ac2d4655f4db85d68b33fe3", "parameters": [{"name": "id", "in": "path", "description": "Role ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/permissions": {"get": {"tags": ["Admin Roles"], "summary": "List permissions", "description": "List Permissions", "operationId": "cb7b8ffd1695732cbcbd29a887b5ed4a", "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/transactions": {"get": {"tags": ["Admin Transactions"], "summary": "List transactions", "description": "List Transactions", "operationId": "205f01ee9f90542f3d9fdc423947dc36", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/transactions/{id}": {"get": {"tags": ["Admin Transactions"], "summary": "View transaction details", "description": "View Transaction", "operationId": "0d84a094102b16bd16e8d634a962ffbd", "parameters": [{"name": "id", "in": "path", "description": "Transaction ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin Transactions"], "summary": "Delete transaction", "description": "Delete Transaction", "operationId": "0625d603abcda0482694deef845f96a4", "parameters": [{"name": "id", "in": "path", "description": "Transaction ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/users": {"get": {"tags": ["Admin Users"], "summary": "List users", "description": "List User", "operationId": "2676d29b73ec7210fd21febdc28828b8", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/users/{id}": {"get": {"tags": ["Admin Users"], "summary": "View user details", "description": "View User", "operationId": "08bc83724c39bd35093c4add2a17a0f4", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin Users"], "summary": "Delete users", "description": "Delete User", "operationId": "328c7ef558e65c6372801fd5d912cc18", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Admin Users"], "summary": "Change user status", "description": "Change User Status", "operationId": "cf8b58a3b3cd45773528e93d7db2b71e", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer"}}, {"name": "purpose", "in": "query", "description": "Restriction Purpose", "required": false, "schema": {"type": "string"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/users/pay_commission/{id}/{referralId}": {"patch": {"tags": ["Admin Users"], "summary": "Pay Commission", "description": "Pay Commission", "operationId": "1e26e7bd5d17070c964490cefb2f3ce2", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer"}}, {"name": "referralId", "in": "path", "description": "Referral ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Edited successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/wallets": {"get": {"tags": ["Admin <PERSON>ets"], "summary": "List wallets", "description": "List Wallets", "operationId": "5585476af2c4164932e510f27203b4de", "parameters": [{"$ref": "#/components/parameters/pageSize"}, {"$ref": "#/components/parameters/pageNumber"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Entries loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/wallets/{id}": {"get": {"tags": ["Admin <PERSON>ets"], "summary": "View wallet details", "description": "View Wallet", "operationId": "168b0196d053c381f11f0e08bde1a7b1", "parameters": [{"name": "id", "in": "path", "description": "Wallet ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Entry loaded successfully"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin <PERSON>ets"], "summary": "Delete wallet", "description": "Delete Wallet", "operationId": "871106960f40b7c9e69317f6790e43fc", "parameters": [{"name": "id", "in": "path", "description": "Wallet ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/wallets/credit/{id}": {"get": {"tags": ["Admin <PERSON>ets"], "summary": "Credit wallet", "description": "Credit Wallet", "operationId": "7fb5544323643a6a7f92e82b0a7080fa", "parameters": [{"name": "id", "in": "path", "description": "Wallet ID", "required": true, "schema": {"type": "integer"}}, {"name": "amount", "in": "query", "description": "Amount", "required": true, "schema": {"type": "float"}}, {"name": "narration", "in": "query", "description": "Narration", "required": true, "schema": {"type": "string"}}, {"name": "reference", "in": "query", "description": "Reference", "required": false, "schema": {"type": "string"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/wallets/debit/{id}": {"get": {"tags": ["Admin <PERSON>ets"], "summary": "Debit wallet", "description": "Debit Wallet", "operationId": "62770e0015fcb69fe1ca0cc87fde8c96", "parameters": [{"name": "id", "in": "path", "description": "Wallet ID", "required": true, "schema": {"type": "integer"}}, {"name": "amount", "in": "query", "description": "Amount", "required": true, "schema": {"type": "float"}}, {"name": "narration", "in": "query", "description": "Narration", "required": true, "schema": {"type": "string"}}, {"name": "reference", "in": "query", "description": "Reference", "required": false, "schema": {"type": "string"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/wallets/lock-amount/{id}": {"get": {"tags": ["Admin <PERSON>ets"], "summary": "Lock wallet amount", "description": "Lock Amount", "operationId": "b9b404bdf60bd46675ed1832cc9b00a5", "parameters": [{"name": "id", "in": "path", "description": "Wallet ID", "required": true, "schema": {"type": "integer"}}, {"name": "amount", "in": "query", "description": "Amount", "required": true, "schema": {"type": "float"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/wallets/unlock-amount/{id}": {"get": {"tags": ["Admin <PERSON>ets"], "summary": "Unlock wallet amount", "description": "Unlock Amount", "operationId": "4492ddffb1c1be768909ebd658bf3e0d", "parameters": [{"name": "id", "in": "path", "description": "Wallet ID", "required": true, "schema": {"type": "integer"}}, {"name": "amount", "in": "query", "description": "Amount", "required": true, "schema": {"type": "float"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/admin/wallets/post-no-debit-status/{id}": {"get": {"tags": ["Admin <PERSON>ets"], "summary": "Lock wallet", "description": "Post-No-Debit Status on Wallet", "operationId": "b52ef9084c23ddca76ddb94b1baa3514", "parameters": [{"name": "id", "in": "path", "description": "Wallet ID", "required": true, "schema": {"type": "integer"}}, {"name": "pin", "in": "query", "description": "<PERSON>n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/register": {"post": {"tags": ["User Authentication"], "summary": "Create an account for user", "description": "Create an account", "operationId": "createUserAccount", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "Account created successful"}}}}, "/api/v1/user/verify-account/{email_address}/{token}": {"get": {"tags": ["User Authentication"], "summary": "Verify Account", "description": "Registration verification", "operationId": "registrationConfirmation", "parameters": [{"name": "email_address", "in": "path", "description": "Registered email address", "required": true, "schema": {"type": "string"}}, {"name": "token", "in": "path", "description": "Token sent to mail", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Verification token confirmed"}}}}, "/api/v1/user/login": {"patch": {"tags": ["User Authentication"], "summary": "Login a user", "description": "<PERSON><PERSON>", "operationId": "loginUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"": {"properties": {"phonenumber": {"type": "string"}, "password": {"type": "string"}}, "type": "object"}}, "type": "object", "example": {"phonenumber": "***********", "password": "mypassword"}}}}}, "responses": {"200": {"description": "Please verify your login to proceed"}}}}, "/api/v1/user/login-renew": {"post": {"tags": ["User Authentication"], "summary": "<PERSON>w Login Authorization", "description": "<PERSON><PERSON>", "operationId": "renewLoginUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"": {"properties": {"password": {"type": "string"}, "deviceId": {"type": "string"}, "deviceName": {"type": "string"}, "deviceOS": {"type": "string"}}, "type": "object"}}, "type": "object", "example": {"password": "mypassword", "deviceId": "*********", "deviceName": "iphone 15 pro max", "deviceOS": "ios18"}}}}}, "responses": {"200": {"description": "Login renewed"}}}}, "/api/v1/user/loginVerify": {"post": {"tags": ["User Authentication"], "summary": "Verify login token", "description": "<PERSON><PERSON>", "operationId": "verifyLoginUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"": {"properties": {"email": {"type": "string"}, "password": {"type": "string"}, "token": {"type": "string"}, "firebaseToken": {"type": "string"}, "deviceId": {"type": "string"}, "deviceName": {"type": "string"}, "deviceOS": {"type": "string"}}, "type": "object"}}, "type": "object", "example": {"phonenumber": "***********", "password": "mypassword", "token": "657483", "firebaseToken": "ios~6474858464373838ruriur789447uyurhru7", "deviceId": "647483838032i8uh3h73y473yg3r665rt3", "deviceName": "iPhone 15 Pro max", "deviceOS": "ios"}}}}}, "responses": {"200": {"description": "<PERSON><PERSON>l"}}}}, "/api/v1/user/create-verification-token": {"post": {"tags": ["User Authentication"], "summary": "Create token for user", "description": "Create token", "operationId": "createAccountToken", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "type", "channel"], "properties": {"userId": {"type": "integer"}, "type": {"type": "string", "enum": ["2fa", "register", "reset"]}, "channel": {"type": "string", "enum": ["sms", "email", "any"]}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Created successfully"}}}}, "/api/v1/user/verify-verification-token": {"post": {"tags": ["User Authentication"], "summary": "Verify user token", "description": "Verify token", "operationId": "verifyVerificationToken", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "type", "verifying"], "properties": {"userId": {"type": "integer"}, "token": {"type": "string", "example": "5463"}, "verifying": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Confirmed successfully"}}}}, "/api/v1/user/check-verification-token": {"post": {"tags": ["User Authentication"], "summary": "Check user token", "description": "Check token", "operationId": "checkVerificationToken", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "token", "verifying"], "properties": {"userId": {"type": "integer"}, "token": {"type": "string"}, "verifying": {"type": "string", "enum": ["2fa", "register", "reset"]}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Confirmed successfully"}}}}, "/api/v1/user/forgot-password/{emailOrPhone}": {"get": {"tags": ["User Authentication"], "summary": "Forgot password", "description": "Forgot Password", "operationId": "userForgotPassword", "parameters": [{"name": "email", "in": "path", "description": "User's email address", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Reset token sent successfuly"}}}}, "/api/v1/user/reset-password": {"post": {"tags": ["User Authentication"], "summary": "Reset password", "description": "Reset Password", "operationId": "userResetPassword", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["userId", "token", "newPassword", "confirmPassword"], "properties": {"userId": {"type": "integer"}, "token": {"type": "string"}, "newPassword": {"type": "string"}, "confirmPassword": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authorization Token Confirmed successfuly"}}}}, "/api/v1/user/list-airtime-provider": {"get": {"tags": ["User Airtime"], "summary": "List Airtime Provider", "operationId": "e7a2eabb2bd0a962bb798aa9e0421a07", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/buy-airtime": {"post": {"tags": ["User Airtime"], "summary": "Buy Airtime", "operationId": "ca4849a94c447a5b70ee9090887e3d29", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuyAirtimeRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/list-banks": {"get": {"tags": ["User Bank"], "summary": "List all banks", "operationId": "04d126afab91adedf7457f0425903ca2", "parameters": [{"name": "currency_id", "in": "query", "description": "currency id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Banks loaded successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/add-bank": {"post": {"tags": ["User Bank"], "summary": "Add Bank to profile", "operationId": "db50c963387387d509f57e770bffe9ea", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddBankRequest"}}}}, "responses": {"200": {"description": "Bank added successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/verify-bank/{accountNumber}/{bankCode}": {"get": {"tags": ["User Bank"], "summary": "Verify Bank", "operationId": "a00229942ac432469760d2195f5f8699", "parameters": [{"name": "accountNumber", "in": "path", "description": "Account number", "required": true, "schema": {"type": "string"}}, {"name": "bankCode", "in": "path", "description": "bankCode", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Bank Validated successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/list-saved-bank": {"get": {"tags": ["User Bank"], "summary": "List account bank", "operationId": "d39ddd5bfb939409e8cda345c8005ad1", "responses": {"200": {"description": "Bank Validated successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/list-bets-provider": {"get": {"tags": ["User Betting"], "summary": "List Betting Provider", "operationId": "53c89c5bebc3349e4fe5e2ebf0ae4190", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/fund-bet-account": {"post": {"tags": ["User Betting"], "summary": "Top up Betting account", "operationId": "f05b1eddbb5bdd2fddc75e699ea81fe9", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FundBettingRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/verify-betting-customer-id/{customerId}/{biller_code}": {"get": {"tags": ["User Betting"], "summary": "Verify Betting Customer ID", "operationId": "f18cb0d497899719d184fb4fc062f9c0", "parameters": [{"name": "customerId", "in": "path", "description": "Customer ID", "required": true, "schema": {"type": "string"}}, {"name": "biller_code", "in": "path", "description": "<PERSON>er <PERSON>", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/list-cable-tv-provider": {"get": {"tags": ["User Cable TV"], "summary": "List Cable Provider", "operationId": "ba4b71b216842563269cec71a29c67a9", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/buy-cable-tv": {"post": {"tags": ["User Cable TV"], "summary": "Buy cable TV", "operationId": "1c90740bd27d44de92d61ebcc6a95ec2", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuyCableRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/verify-cable-tv/{smartcard}/{biller_code}": {"get": {"tags": ["User Cable TV"], "summary": "Verify Smartcard", "operationId": "f21a067d04942c4d136b31f459ded8b5", "parameters": [{"name": "smartcard", "in": "path", "description": "Smartcard number", "required": true, "schema": {"type": "string"}}, {"name": "biller_code", "in": "path", "description": "<PERSON>er <PERSON>", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/list-data-provider": {"get": {"tags": ["User DATA"], "summary": "List Airtime Provider", "operationId": "707b7e00bd7211ba974ff69e91e8b14d", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/buy-data": {"post": {"tags": ["User DATA"], "summary": "Buy Data", "operationId": "98bb74efece070f942b907929e53ca00", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuyDataRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/list-electricity-bill-provider": {"get": {"tags": ["User Electricity Bill"], "summary": "List Electicity Bill Provider", "operationId": "3815f9fccdde1cdba2b74fc432c69203", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/buy-electricity-bill": {"post": {"tags": ["User Electricity Bill"], "summary": "Buy cable TV", "operationId": "b3a2419b8ab7eb1d2b87b1e11a80b9f0", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuyElectricityBillRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/verify-meter-number/{meter_number}/{biller_code}": {"get": {"tags": ["User Electricity Bill"], "summary": "Verify Meter Number", "operationId": "1f36344e24ab2c5c4511101f4da1cc47", "parameters": [{"name": "metreNumber", "in": "path", "description": "Meter number", "required": true, "schema": {"type": "string"}}, {"name": "biller_code", "in": "path", "description": "<PERSON>er <PERSON>", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/upload-passport": {"post": {"tags": ["User Profile"], "summary": "Upload Passport Photograp", "operationId": "0383dbbdafe860cc3bec00cdc1947731", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PassportRequest"}}}}, "responses": {"200": {"description": "Request sent"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/update-other-name": {"post": {"tags": ["User Profile"], "summary": "Update other names", "operationId": "c9a1ebd8b828b6219efa27644624d534", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OthernameRequest"}}}}, "responses": {"200": {"description": "Request sent"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/upload-bvn": {"post": {"tags": ["User Profile"], "summary": "Upload bvn", "operationId": "fe8768f02452f5025b5fdc72d190e6c1", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BvnRequest"}}}}, "responses": {"200": {"description": "Request sent"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/resend-bvn-token": {"get": {"tags": ["User Profile"], "summary": "Resend bvn token", "operationId": "040772d187665ff26b5294a664730020", "responses": {"200": {"description": "Request sent"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/update-address": {"get": {"tags": ["User Profile"], "summary": "Update profile address", "operationId": "3b6633ebc514d93482ea839b1e7ed4cf", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/upload-proof-of-address": {"post": {"tags": ["User Profile"], "summary": "Add proof of address to user's account", "operationId": "a53744dcb68f000a1822df568024bfc0", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddProofOfAddressRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/change-password": {"post": {"tags": ["User Profile"], "summary": "Change account password", "operationId": "e87f68ed5e0cb7b41649588565c28539", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/change-pin": {"post": {"tags": ["User Profile"], "summary": "Change account transaction pin", "operationId": "d71274e4146e819037b1906a5466de37", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePinRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/logout/{deviceId}": {"get": {"tags": ["User Profile"], "summary": "Sign out", "operationId": "ad5406dce1c95101babc162b911c1a6b", "parameters": [{"name": "deviceId", "in": "path", "description": "device Id", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Request Successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile": {"get": {"tags": ["User Profile"], "summary": "Update profile address", "operationId": "962b7b5528f749f0bf6be512ef13f40d", "responses": {"200": {"description": "Request sent"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/generate-bio-key": {"post": {"tags": ["User Profile"], "summary": "Generate biometrics key", "operationId": "681eb34933dfaca1637685904a8b4249", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateBiometricsKeyRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/notifications": {"patch": {"tags": ["User Profile"], "summary": "List notifications", "operationId": "8a79fccc72a401f0f04cc5fee15aebbc", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilterListRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/notifications/{id}": {"get": {"tags": ["User Profile"], "summary": "Single notifications", "operationId": "aab085fb2923d72da163d3d96f82f6a2", "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/notifications-mark-all": {"post": {"tags": ["User Profile"], "summary": "Mark notification read", "operationId": "9e09e15b560a6c450dcbc1c13b343735", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserMarkNotificationRequest"}}}}, "responses": {"200": {"description": "Request successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/list-transactions": {"patch": {"tags": ["User Transactions"], "summary": "List transactions for user", "operationId": "510cbfce6836cd8888d5188fd532705e", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilterListRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/single-transaction/{transactionId}": {"get": {"tags": ["User Transactions"], "summary": "Load transaction for user", "operationId": "3e211e9f7965add54f5672053bb7a25f", "parameters": [{"name": "transactionId", "in": "path", "description": "Transaction ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}, "/api/v1/user/profile/withdraw-funds": {"post": {"tags": ["User Wallet"], "summary": "Withdraw fund request", "operationId": "8aa24cb6edd2e8f0f044c6bd5b6a31a3", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WithdrawFundRequest"}}}}, "responses": {"200": {"description": "Transaction successful"}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"RegisterRequest": {"required": ["firstname", "lastname", "phonenumber", "email", "password", "transactionPin"], "properties": {"firstname": {"description": "First name", "type": "string", "example": "john"}, "lastname": {"description": "Lastname", "type": "string", "example": "doe"}, "phonenumber": {"description": "Phone number", "type": "string", "example": "09012346574"}, "email": {"description": "Email", "type": "string", "example": "<EMAIL>"}, "password": {"description": "Password", "type": "string", "example": "hjdhkjlkddk"}, "transactionPin": {"description": "<PERSON>n", "type": "string", "example": "1222"}}, "type": "object"}, "AdminCreateAdminRequest": {"required": ["email", "firstname", "lastname", "phonenumber", "gender", "role_id"], "properties": {"email": {"description": "Email address", "type": "string"}, "firstname": {"description": "First name", "type": "string"}, "lastname": {"description": "First name", "type": "string"}, "phonenumber": {"description": "Phone number", "type": "string"}, "gender": {"description": "Gender", "type": "string"}, "role_id": {"description": "Admin role Id", "type": "integer"}}, "type": "object"}, "AdminCreateApiKeyRequest": {"required": ["provider", "client_secret"], "properties": {"provider": {"description": "API provider", "type": "string"}, "client_id": {"description": "API Client key", "type": "string"}, "client_code": {"description": "API Client code", "type": "string"}, "client_secret": {"description": "API Client secret", "type": "string"}, "endpoint": {"description": "API Endpoint", "type": "string"}, "secondary_endpoint": {"description": "API Secondary Endpoint", "type": "string"}, "data": {"description": "API JSON Data", "type": "string"}}, "type": "object"}, "AdminCreateKycLevelRequest": {"required": ["name", "level", "maximum_one_time_withdrawal", "maximum_daily_withdrawal", "maximum_wallet_balance"], "properties": {"name": {"description": "Name", "type": "string"}, "level": {"description": "KYC Level", "type": "integer"}, "maximum_one_time_withdrawal": {"description": "Maximum one time withdrawal", "type": "number", "format": "float"}, "maximum_daily_withdrawal": {"description": "Maximum daily withdrawal", "type": "number", "format": "float"}, "maximum_wallet_balance": {"description": "Maximum wallet balance", "type": "number", "format": "float"}}, "type": "object"}, "AdminCreatePermissionRoleRequest": {"required": ["name", "description", "permissions"], "properties": {"name": {"description": "Name", "type": "string"}, "description": {"description": "Description", "type": "string"}, "permissions": {"type": "array", "items": {"description": "ID", "type": "integer"}}}, "type": "object"}, "AdminEditAdminRequest": {"required": ["email", "firstname", "lastname", "phonenumber", "gender", "role_id"], "properties": {"email": {"description": "Email address", "type": "string"}, "firstname": {"description": "First name", "type": "string"}, "lastname": {"description": "First name", "type": "string"}, "phonenumber": {"description": "Phone number", "type": "string"}, "gender": {"description": "Gender", "type": "string"}, "role_id": {"description": "Admin role Id", "type": "integer"}}, "type": "object"}, "ChangePasswordRequest": {"required": ["currentPassword", "newPassword", "confirmPassword"], "properties": {"currentPassword": {"description": "Current password", "type": "string", "example": "ejkdulikdgdjh@173783#h"}, "newPassword": {"description": "New Password", "type": "string", "example": "yhetgnhjeljhkee6878@1@33#"}, "confirmPassword": {"description": "Confirm new password", "type": "string", "example": "yhetgnhjeljhkee6878@1@33#"}}, "type": "object"}, "FilterListRequest": {"properties": {"pageSize": {"type": "integer"}, "pageNumber": {"type": "integer"}, "search": {"type": "string"}}, "type": "object"}, "UserAddBankRequest": {"required": ["bankName", "bankCode", "accountNumber", "pin"], "properties": {"bankName": {"description": "Bank name", "type": "string", "example": "<PERSON><PERSON>"}, "bankCode": {"description": "Bank Code", "type": "string", "example": "057"}, "accountNumber": {"description": "Account Number", "type": "string"}, "pin": {"description": "PIN", "type": "string", "example": "1234"}}, "type": "object"}, "AddProofOfAddressRequest": {"required": ["proofOfAddress", "proofOfAddressUpload"], "properties": {"proofOfAddress": {"description": "Proof of address", "type": "string", "example": 1}, "proofOfAddressUpload": {"description": "Proof of address Base 64 string", "type": "string", "example": ""}}, "type": "object"}, "AddressRequest": {"required": ["address"], "properties": {"address": {"description": "Address", "type": "string", "example": "home"}}, "type": "object"}, "BuyAirtimeRequest": {"required": ["phonenumber", "amount", "provider_code", "provider_name"], "properties": {"phonenumber": {"description": "Phone number", "type": "string"}, "amount": {"description": "Amount", "type": "number", "format": "float"}, "provider_code": {"description": "Provider Code", "type": "string"}, "provider_name": {"description": "Provider Name", "type": "string"}, "card_id": {"description": "Card ID", "type": "integer"}}, "type": "object"}, "BuyCableRequest": {"required": ["phonenumber", "smartcard_number", "biller_code", "biller_name", "plan_code", "plan_name"], "properties": {"phonenumber": {"description": "Phone number", "type": "string"}, "smartcard_number": {"description": "Smartcard number", "type": "string"}, "biller_code": {"description": "<PERSON>er <PERSON>", "type": "string"}, "biller_name": {"description": "Biller Name", "type": "string"}, "plan_code": {"description": "Plan Code", "type": "string"}, "plan_name": {"description": "Plan Name", "type": "string"}, "card_id": {"description": "Card ID", "type": "integer"}}, "type": "object"}, "BuyDataRequest": {"required": ["phonenumber", "provider_code", "provider_name", "data_plan_code", "data_plan_name"], "properties": {"phonenumber": {"description": "Phone number", "type": "string"}, "provider_code": {"description": "Provider Code", "type": "string"}, "provider_name": {"description": "Provider Name", "type": "string"}, "data_plan_code": {"description": "Plan Code", "type": "string"}, "data_plan_name": {"description": "Plan Name", "type": "string"}, "card_id": {"description": "Card ID", "type": "integer"}}, "type": "object"}, "BuyElectricityBillRequest": {"required": ["phonenumber", "metreNumber", "biller_code", "biller_name", "plan_code", "plan_name", "amount"], "properties": {"phonenumber": {"description": "Phone number", "type": "string"}, "metreNumber": {"description": "metre number", "type": "string"}, "biller_code": {"description": "<PERSON>er <PERSON>", "type": "string"}, "biller_name": {"description": "Biller Name", "type": "string"}, "plan_code": {"description": "Plan Code", "type": "string"}, "plan_name": {"description": "Plan Name", "type": "string"}, "amount": {"description": "Amount", "type": "number", "format": "float"}, "card_id": {"description": "Card ID", "type": "integer"}}, "type": "object"}, "BvnRequest": {"required": ["bvn"], "properties": {"bvn": {"description": "BVN", "type": "string", "example": "doe"}}, "type": "object"}, "ChangePinRequest": {"required": ["currentPin", "newPin", "confirmPin"], "properties": {"currentPin": {"description": "Current Pin", "type": "string", "example": "0000"}, "newPin": {"description": "New Pin", "type": "string", "example": "1111"}, "confirmPin": {"description": "Confirm new pin", "type": "string", "example": "1111"}}, "type": "object"}, "FundBettingRequest": {"required": ["customerId", "amount", "provider_code", "provider_name"], "properties": {"customerId": {"description": "Customer ID", "type": "string"}, "amount": {"description": "Amount", "type": "number", "format": "float"}, "provider_code": {"description": "Provider Code", "type": "string"}, "provider_name": {"description": "Provider Name", "type": "string"}, "card_id": {"description": "Card ID", "type": "integer"}}, "type": "object"}, "GenerateBiometricsKeyRequest": {"required": ["deviceId", "pin"], "properties": {"deviceId": {"description": "Device ID", "type": "string", "example": ""}, "pin": {"description": "Transaction Pin", "type": "string", "example": "0000"}}, "type": "object"}, "UserMarkNotificationRequest": {"required": ["notifications"], "properties": {"notifications": {"description": "Array of notification IDs", "type": "array", "items": {"type": "string", "format": "uuid", "example": "e74d6132-d76e-11eb-b8bc-0242ac130003"}}}, "type": "object"}, "OthernameRequest": {"required": ["name"], "properties": {"name": {"description": "Other names", "type": "string", "example": "doe"}}, "type": "object"}, "PassportRequest": {"required": ["passport"], "properties": {"passport": {"description": "Base 64 passport strings", "type": "string", "example": "base64 data uri string"}}, "type": "object"}, "WithdrawFundRequest": {"required": ["walletId", "amount", "pin", "toBankId"], "properties": {"walletId": {"description": "Wallet ID", "type": "integer", "example": 1}, "amount": {"description": "amount", "type": "numeric", "example": 5000}, "pin": {"description": "PIN", "type": "string", "example": "1234"}, "toBankId": {"description": "Bank ID to transfer to", "type": "integer", "example": 1}}, "type": "object"}}, "parameters": {"pageSize": {"name": "pageSize", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer"}}, "pageNumber": {"name": "pageNumber", "in": "query", "description": "Current page", "required": false, "schema": {"type": "integer"}}, "search": {"name": "query", "in": "query", "description": "Filter parameters", "required": false, "schema": {"type": "string"}}, "status": {"name": "status", "in": "query", "description": "Filter status", "required": false, "schema": {"type": "string"}}}, "securitySchemes": {"bearerAuth": {"type": "http", "name": "Authorization", "in": "header", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Admin Au<PERSON>ntication", "description": "Admin Au<PERSON>ntication"}, {"name": "Admin", "description": "Admin"}, {"name": "Admin API Keys", "description": "Admin API Keys"}, {"name": "Admin Devices", "description": "Admin Devices"}, {"name": "Admin KYC Levels", "description": "Admin KYC Levels"}, {"name": "Admin Profile", "description": "Admin Profile"}, {"name": "User Profile", "description": "User Profile"}, {"name": "Admin Roles", "description": "Admin Roles"}, {"name": "Admin Transactions", "description": "Admin Transactions"}, {"name": "Admin Users", "description": "Admin Users"}, {"name": "Admin <PERSON>ets", "description": "Admin <PERSON>ets"}, {"name": "User Authentication", "description": "User Authentication"}, {"name": "User Airtime", "description": "User Airtime"}, {"name": "User Bank", "description": "User Bank"}, {"name": "User Betting", "description": "User Betting"}, {"name": "User Cable TV", "description": "User Cable TV"}, {"name": "User DATA", "description": "User DATA"}, {"name": "User Electricity Bill", "description": "User Electricity Bill"}, {"name": "User Transactions", "description": "User Transactions"}, {"name": "User Wallet", "description": "User Wallet"}]}