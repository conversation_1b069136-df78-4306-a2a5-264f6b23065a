{"name": "@telegraf/types", "private": false, "version": "7.1.0", "description": "Type declarations for the Telegram API", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/telegraf/types.git"}, "keywords": ["telegram", "telegraf", "bot", "api", "types", "typings"], "scripts": {"prepare": "deno task backport"}, "author": "Telegraf contributors", "types": "index.d.ts", "license": "MIT", "bugs": {"url": "https://github.com/telegraf/types/issues"}, "homepage": "https://github.com/telegraf/types#readme", "files": ["*.d.ts", "index.js"], "devDependencies": {"deno-bin": "^1.40.4"}}