import dotenv from 'dotenv';
import fetch from 'node-fetch';
import { encryptRequest } from '../../encryption.js';
import { getUser } from '../database/database.js';

dotenv.config();

const BASE_URL = process.env.SETUP_API_URL;

// Get user profile information
export const getUserProfile = async (ctx, chatId) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to view your profile.");
        return null;
    }

    try {
        const response = await fetch(BASE_URL + '/profile', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            }
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Profile data retrieved successfully:', data);
            return data.data;
        } else {
            console.log('❌ Failed to retrieve profile data:', data);
            await ctx.reply(`❌ Failed to retrieve profile: ${data.message || 'Unknown error'}`);
            return null;
        }
    } catch (error) {
        console.error('Profile retrieval error:', error);
        await ctx.reply("⚠️ An error occurred while retrieving your profile. Please try again later.");
        return null;
    }
};

// Update user's other name
export const updateOtherName = async (ctx, chatId, name) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to update your profile.");
        return false;
    }

    const requestBody = JSON.stringify({ name });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch(BASE_URL + '/profile/update-other-name', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Other name updated successfully:', data);
            await ctx.reply("✅ Your other name has been updated successfully!");
            return true;
        } else {
            console.log('❌ Failed to update other name:', data);
            await ctx.reply(`❌ Failed to update other name: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Update other name error:', error);
        await ctx.reply("⚠️ An error occurred while updating your other name. Please try again later.");
        return false;
    }
};

// Update user's address
export const updateAddress = async (ctx, chatId, address) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to update your address.");
        return false;
    }

    const requestBody = JSON.stringify({ address });
    const encryptedData = encryptRequest(requestBody);

    try {
        // Changed to POST method since we need to send a request body
        const response = await fetch(BASE_URL + '/profile/update-address', {
            method: 'POST', // Changed from GET to POST
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Address updated successfully:', data);
            await ctx.reply("✅ Your address has been updated successfully!");
            return true;
        } else {
            console.log('❌ Failed to update address:', data);
            await ctx.reply(`❌ Failed to update address: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Update address error:', error);
        await ctx.reply("⚠️ An error occurred while updating your address. Please try again later.");
        return false;
    }
};

// Upload passport photo
export const uploadPassport = async (ctx, chatId, passportBase64) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to upload your passport photo.");
        return false;
    }

    const requestBody = JSON.stringify({ passport: passportBase64 });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch(BASE_URL + '/profile/upload-passport', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Passport photo uploaded successfully:', data);
            await ctx.reply("✅ Your passport photo has been uploaded successfully!");
            return true;
        } else {
            console.log('❌ Failed to upload passport photo:', data);
            await ctx.reply(`❌ Failed to upload passport photo: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Upload passport photo error:', error);
        await ctx.reply("⚠️ An error occurred while uploading your passport photo. Please try again later.");
        return false;
    }
};

// Upload BVN
export const uploadBVN = async (ctx, chatId, bvn) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to upload your BVN.");
        return false;
    }

    const requestBody = JSON.stringify({ bvn });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch(BASE_URL + '/profile/upload-bvn', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        // Check if response is JSON before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const textResponse = await response.text();
            console.error('❌ Server returned non-JSON response:', textResponse);
            await ctx.reply("❌ Server error: Invalid response format. Please try again later.");
            return false;
        }

        const data = await response.json();
        if (response.ok) {
            console.log('✅ BVN uploaded successfully:', data);
            await ctx.reply("✅ Your BVN has been uploaded successfully! You may need to verify it with a token sent to you.");
            return true;
        } else {
            console.log('❌ Failed to upload BVN:', data);
            await ctx.reply(`❌ Failed to upload BVN: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Upload BVN error:', error);
        await ctx.reply("⚠️ An error occurred while uploading your BVN. Please try again later.");
        return false;
    }
};

// Resend BVN verification token
export const resendBVNToken = async (ctx, chatId) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to request a BVN verification token.");
        return false;
    }

    try {
        const response = await fetch(BASE_URL +'/profile/resend-bvn-token', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            }
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ BVN token resent successfully:', data);
            await ctx.reply("✅ A new BVN verification token has been sent to you.");
            return true;
        } else {
            console.log('❌ Failed to resend BVN token:', data);
            await ctx.reply(`❌ Failed to resend BVN token: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Resend BVN token error:', error);
        await ctx.reply("⚠️ An error occurred while requesting a new BVN token. Please try again later.");
        return false;
    }
};

// Upload proof of address
export const uploadProofOfAddress = async (ctx, chatId, proofType, proofBase64) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to upload proof of address.");
        return false;
    }

    const requestBody = JSON.stringify({
        proofOfAddress: proofType,
        proofOfAddressUpload: proofBase64
    });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch(BASE_URL + '/profile/upload-proof-of-address', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Proof of address uploaded successfully:', data);
            await ctx.reply("✅ Your proof of address has been uploaded successfully!");
            return true;
        } else {
            console.log('❌ Failed to upload proof of address:', data);
            await ctx.reply(`❌ Failed to upload proof of address: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Upload proof of address error:', error);
        await ctx.reply("⚠️ An error occurred while uploading your proof of address. Please try again later.");
        return false;
    }
};

// Change password
export const changePassword = async (ctx, chatId, currentPassword, newPassword, confirmPassword) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to change your password.");
        return false;
    }

    if (newPassword !== confirmPassword) {
        await ctx.reply("❌ New password and confirmation do not match.");
        return false;
    }

    const requestBody = JSON.stringify({
        currentPassword,
        newPassword,
        confirmPassword
    });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch(BASE_URL + '/profile/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Password changed successfully:', data);
            await ctx.reply("✅ Your password has been changed successfully!");
            return true;
        } else {
            console.log('❌ Failed to change password:', data);
            await ctx.reply(`❌ Failed to change password: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Change password error:', error);
        await ctx.reply("⚠️ An error occurred while changing your password. Please try again later.");
        return false;
    }
};

// Change transaction PIN
export const changePin = async (ctx, chatId, currentPin, newPin, confirmPin) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to change your transaction PIN.");
        return false;
    }

    if (newPin !== confirmPin) {
        await ctx.reply("❌ New PIN and confirmation do not match.");
        return false;
    }

    const requestBody = JSON.stringify({
        currentPin,
        newPin,
        confirmPin
    });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch(BASE_URL + '/profile/change-pin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Transaction PIN changed successfully:', data);
            await ctx.reply("✅ Your transaction PIN has been changed successfully!");
            return true;
        } else {
            console.log('❌ Failed to change transaction PIN:', data);
            await ctx.reply(`❌ Failed to change transaction PIN: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Change transaction PIN error:', error);
        await ctx.reply("⚠️ An error occurred while changing your transaction PIN. Please try again later.");
        return false;
    }
};

// Logout from a specific device
export const logoutDevice = async (ctx, chatId, deviceId) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to logout from a device.");
        return false;
    }

    try {
        const response = await fetch(BASE_URL + `/profile/logout/${deviceId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            }
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Logged out from device successfully:', data);
            await ctx.reply("✅ You have been logged out from the specified device.");
            return true;
        } else {
            console.log('❌ Failed to logout from device:', data);
            await ctx.reply(`❌ Failed to logout from device: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Logout from device error:', error);
        await ctx.reply("⚠️ An error occurred while logging out from the device. Please try again later.");
        return false;
    }
};

// Generate biometrics key
export const generateBioKey = async (ctx, chatId, bioKey) => {
    const userData = getUser(chatId);
    if (!userData || !userData.access_token) {
        await ctx.reply("⚠️ You need to be logged in to generate a biometrics key.");
        return false;
    }

    const requestBody = JSON.stringify({ bioKey });
    const encryptedData = encryptRequest(requestBody);

    try {
        const response = await fetch(BASE_URL + '/profile/generate-bio-key', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userData.access_token}`
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Biometrics key generated successfully:', data);
            await ctx.reply("✅ Your biometrics key has been generated successfully!");
            return true;
        } else {
            console.log('❌ Failed to generate biometrics key:', data);
            await ctx.reply(`❌ Failed to generate biometrics key: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Generate biometrics key error:', error);
        await ctx.reply("⚠️ An error occurred while generating your biometrics key. Please try again later.");
        return false;
    }
};

// Format profile data for display
export const formatProfileData = (profileData) => {
    if (!profileData) return "No profile data available.";
    
    const user = profileData.user || profileData;
    
    let formattedProfile = "👤 *Your Profile Information*\n\n";
    
    if (user.firstname) formattedProfile += `*First Name:* ${user.firstname}\n`;
    if (user.lastname) formattedProfile += `*Last Name:* ${user.lastname}\n`;
    if (user.othername) formattedProfile += `*Other Name:* ${user.othername}\n`;
    if (user.email) formattedProfile += `*Email:* ${user.email}\n`;
    if (user.phonenumber) formattedProfile += `*Phone:* ${user.phonenumber}\n`;
    if (user.address) formattedProfile += `*Address:* ${user.address}\n`;
    if (user.bvn) formattedProfile += `*BVN:* ${user.bvn}\n`;
    if (user.bvn_verified_at) formattedProfile += `*BVN Verified:* ✅\n`;
    if (user.passport_photo) formattedProfile += `*Passport Photo:* Uploaded ✅\n`;
    if (user.proof_of_address_upload) formattedProfile += `*Proof of Address:* Uploaded ✅\n`;
    
    // Add verification status
    formattedProfile += `\n*Account Status:*\n`;
    formattedProfile += `Email Verified: ${user.email_verified_at ? '✅' : '❌'}\n`;
    formattedProfile += `Phone Verified: ${user.phonenumber_verified_at ? '✅' : '❌'}\n`;
    formattedProfile += `KYC Verified: ${user.kyc_level_id > 1 ? '✅' : '❌'}\n`;
    
    return formattedProfile;
};