import fetch from 'node-fetch';
import { encryptRequest } from '../../encryption.js';
import { saveUser, getUser } from '../database/database.js';

export const verifyLogin = async (ctx, chatId, token) => {
    console.log('Starting login verification process...');
    
    const userData = getUser(chatId);
    if (!userData) {
        console.log('❌ User data not found in database for chatId:', chatId);
        await ctx.reply("⚠️ User data not found. Please login again.");
        return false;
    }
    
    console.log('Retrieved user data:', {
        phone: userData.phone,
        deviceId: userData.device_id,
        deviceName: userData.device_name,
        deviceOS: userData.device_os
    });

    const requestBody = {
        email: userData.email,
        phonenumber: userData.phone,
        password: userData.password,
        token: token,
        firebaseToken: "", // Left empty as specified
        deviceId: userData.device_id,
        deviceName: userData.device_name,
        deviceOS: userData.device_os
    };

    console.log('Verification Request Body:', requestBody);
    
    // Encrypt the request body
    const encryptedData = encryptRequest(JSON.stringify(requestBody));
    console.log('Encrypted Verification Data:', encryptedData);

    try {
        console.log('Sending verification request to server...');
        const response = await fetch('https://api.doubledata.ng/api/v1/user/loginVerify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        console.log('Server Response:', data);
        
        if (response.ok) {
            console.log('✅ Login verification successful!');

            const { access_token, expires_at } = data.data;
            const deviceInfo = {
                deviceId: userData.device_id,
                deviceName: userData.device_name,
                deviceOS: userData.device_os
            };
            
            // Pass all required parameters to saveUser
            await saveUser(
                chatId,
                userData.email,
                userData.phone,
                userData.password,
                deviceInfo,
                access_token,
                expires_at
            );
            
            await ctx.reply("✅ Login verification successful! You're now fully authenticated.");
            return true;
        } else {
            console.log('❌ Verification failed:', data);
            await ctx.reply(`❌ Verification failed: ${data.message || 'Invalid token'}`);
            return false;
        }
    } catch (error) {
        console.error('Verification Error:', error);
        await ctx.reply("⚠️ An error occurred during verification. Please try again later.");
        return false;
    }
};