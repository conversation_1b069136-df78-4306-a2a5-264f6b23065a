import fetch from 'node-fetch';
import { encryptRequest } from '../../encryption.js';
import { saveUser, getUser } from '../database/database.js';
import { generateDeviceInfo } from '../device/deviceInfo.js';

export const loginUser = async (ctx, chatId, users) => {
    const { email, phone, password } = users[chatId];
    const deviceInfo = generateDeviceInfo(ctx);
    
    console.log('Login Data:', phone, password);
    const requestBody = JSON.stringify({ email, phonenumber: phone, password });
    console.log('Login Request:', requestBody);
    const encryptedData = encryptRequest(requestBody);
    console.log('Encrypted Data:', encryptedData);

    try {
        const response = await fetch('https://api.doubledata.ng/api/v1/user/login', {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ Login Successful! User Data:', data);
            
            // Save user data to SQLite database
            await saveUser(chatId, email, phone, password, deviceInfo);
            
            // Ask for verification token with resend option
            await ctx.reply(
                "Enter the code we sent to your email. Don't see it? Check your spam folder or request a new code.",
                {
                    reply_markup: {
                        inline_keyboard: [
                            [{ text: "🔄 Resend Code", callback_data: "resend_otp" }]
                        ]
                    }
                }
            );
            users[chatId].step = 'awaiting_token';
            users[chatId].deviceInfo = deviceInfo;
        } else {
            await ctx.reply(`❌ Login failed: ${data.message || 'Invalid credentials'}`);
            console.log('Login Error:', data);
            delete users[chatId];
        }
    } catch (error) {
        console.error('Login Error:', error);
        await ctx.reply("⚠️ An error occurred while logging in. Please try again later.");
        delete users[chatId];
    }
};

// Function to resend OTP
export const resendOTP = async (ctx, chatId) => {
    const userData = getUser(chatId);
    if (!userData) {
        await ctx.reply("⚠️ Your session has expired. Please login again.");
        return false;
    }
    
    console.log('Resending OTP for user:', userData.email);
    const requestBody = JSON.stringify({ 
        email: userData.email, 
        phonenumber: userData.phone, 
        password: userData.password 
    });
    
    const encryptedData = encryptRequest(requestBody);
    
    try {
        const response = await fetch('https://api.doubledata.ng/api/v1/user/login', {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(encryptedData)
        });

        const data = await response.json();
        if (response.ok) {
            console.log('✅ OTP Resent Successfully!');
            await ctx.reply("✅ A new verification code has been sent to your email. Please check and enter it here.");
            return true;
        } else {
            console.log('❌ Failed to resend OTP:', data);
            await ctx.reply(`❌ Failed to resend code: ${data.message || 'Unknown error'}`);
            return false;
        }
    } catch (error) {
        console.error('Resend OTP Error:', error);
        await ctx.reply("⚠️ An error occurred while resending the code. Please try again later.");
        return false;
    }
};