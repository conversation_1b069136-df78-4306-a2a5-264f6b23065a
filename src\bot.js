import { Telegraf, Mark<PERSON> } from 'telegraf';
import dotenv from 'dotenv';
import fetch from 'node-fetch';
import { loginUser, resendOTP } from './api/login.js';
import { verifyLogin } from './api/loginVerify.js';
import { getBalance } from './api/balance.js';
import { getUser, isTokenExpired } from './database/database.js';
import { registerUser, verifyAccount, forgotPassword, resetPassword, resendVerificationEmail } from './api/register.js';
import { 
    getUserProfile, 
    updateOtherName,
    updateAddress, 
    uploadPassport, 
    uploadBVN, 
    resendBVNToken, 
    uploadProofOfAddress, 
    changePassword, 
    changePin, 
    formatProfileData 
} from './api/profile.js';
import './database/database.js';

dotenv.config();

const bot = new Telegraf(process.env.BOT_TOKEN);
const users = {}; // Store user login data temporarily

const getAuthenticatedMenu = (username) => {
    return Markup.inlineKeyboard([
        [Markup.button.callback("💰 Check Balance", "check_balance")],
        [Markup.button.callback("👤 My Profile", "view_profile")],
        [Markup.button.callback("📱 Buy Airtime", "buy_airtime")],
        [Markup.button.callback("📡 Buy Data", "buy_data")],
        [Markup.button.callback("📞 Contact Support", "contact_support")],
        [Markup.button.callback("🚪 Logout", "logout")]
    ]);
};

const getUnauthenticatedMenu = () => {
    return Markup.inlineKeyboard([
        [Markup.button.callback("🚀 GET STARTED", "get_started")],
        [Markup.button.callback("📝 Register New Account", "register")],
        [Markup.button.callback("✅ Verify Account", "verify_account")],
        [Markup.button.callback("🔑 Forgot Password", "forgot_password")],
        [Markup.button.callback("📞 Contact Support", "contact_support")]
    ]);
};

bot.start(async (ctx) => {
    const chatId = ctx.chat.id;
    const username = ctx.from.username || "User";
    
    const userData = getUser(chatId);
    if (userData && userData.access_token && !isTokenExpired(userData.token_expires_at)) {
        // User is authenticated and token is valid
        // Check if profile is complete before showing main menu
        const profileData = await getUserProfile(ctx, chatId);
        if (profileData) {
            const requiredFields = [
                { key: 'address', name: 'Address' },
                { key: 'bvn', name: 'BVN' },
                { key: 'passport_url', name: 'Passport Photo' },
                { key: 'proof_of_address_url', name: 'Proof of Address' }
            ];
            
            const missingFields = requiredFields.filter(field => !profileData.user[field.key]);
            
            if (missingFields.length > 0) {
                // Profile is incomplete, start the mandatory profile setup
                await ctx.reply(
                    `👋 Welcome back ${username}!\n\n` +
                    "🔐 *Profile Setup Required*\n\n" +
                    "To access all features, you need to complete your profile setup.",
                    { parse_mode: 'Markdown' }
                );
                
                // Show which fields are missing
                let message = "Please complete the following required information:\n\n";
                missingFields.forEach(field => {
                    message += `• ${field.name}\n`;
                });
                await ctx.reply(message);
                
                // Start the profile completion wizard automatically
                await ctx.reply(
                    "Let's complete your profile now:",
                    Markup.inlineKeyboard([
                        [Markup.button.callback("📝 Start Profile Setup", "complete_profile_wizard")]
                    ])
                );
            } else {
                // Profile is complete, show main menu
                await ctx.reply(
                    `👋 Welcome back ${username}!\n\nWhat would you like to do today?`,
                    getAuthenticatedMenu(username)
                );
            }
        } else {
            // Failed to get profile data, show main menu anyway
            await ctx.reply(
                `👋 Welcome back ${username}!\n\nWhat would you like to do today?`,
                getAuthenticatedMenu(username)
            );
        }
    } else {
        // User is not authenticated or token is expired
        await ctx.reply(
            `👋 Hello ${username} 👻!\n\nYou are not signed in.\nTap \"🚀 GET STARTED\" to continue.`,
            getUnauthenticatedMenu()
        );
    }
});

bot.action('get_started', async (ctx) => {
    await ctx.answerCbQuery();
    const emailMessage = await ctx.reply("📧 Send your email to proceed:", {
        reply_markup: { force_reply: true, input_field_placeholder: "<EMAIL>" }
    });
    users[ctx.chat.id] = { step: 'awaiting_email', lastMessageId: emailMessage.message_id };
});

bot.action('check_balance', async (ctx) => {
    await ctx.answerCbQuery();
    // Check if profile is complete before allowing balance check
    await requireCompleteProfile(ctx, async () => {
        await getBalance(ctx, ctx.chat.id);
    });
});

// Helper function to handle profile view and updates
const handleViewProfile = async (ctx, skipAnswerCbQuery = false) => {
    if (!skipAnswerCbQuery) {
        await ctx.answerCbQuery();
    }
    
    const chatId = ctx.chat.id;
    const userData = getUser(chatId);
    
    if (userData && !isTokenExpired(userData.token_expires_at)) {
        // Fetch complete profile from API
        const profileData = await getUserProfile(ctx, chatId);
        
        if (profileData) {
            // Format profile data
            const formattedProfile = formatProfileData(profileData);
            
            // Delete any previous success messages if they exist
            if (users[chatId] && users[chatId].successMessageId) {
                try {
                    await ctx.telegram.deleteMessage(chatId, users[chatId].successMessageId);
                    users[chatId].successMessageId = null;
                } catch (error) {
                    console.error("Error deleting success message:", error);
                }
            }
            
            // Check if we already have a profile message to update
            if (users[chatId] && users[chatId].profileMessageId) {
                try {
                    // Try to update existing profile message
                    await ctx.telegram.editMessageText(
                        chatId,
                        users[chatId].profileMessageId,
                        null,
                        formattedProfile,
                        { parse_mode: 'Markdown' }
                    );
                } catch (error) {
                    console.error("Error updating profile message:", error);
                    
                    // Try to delete the old message first
                    try {
                        await ctx.telegram.deleteMessage(chatId, users[chatId].profileMessageId);
                    } catch (deleteError) {
                        console.error("Error deleting old profile message:", deleteError);
                    }
                    
                    // Send a new message
                    const profileMsg = await ctx.reply(formattedProfile, { parse_mode: 'Markdown' });
                    users[chatId] = users[chatId] || {};
                    users[chatId].profileMessageId = profileMsg.message_id;
                }
            } else {
                // Send new profile message
                const profileMsg = await ctx.reply(formattedProfile, { parse_mode: 'Markdown' });
                users[chatId] = users[chatId] || {};
                users[chatId].profileMessageId = profileMsg.message_id;
            }
            
            // Store profile data
            users[chatId].profileData = profileData;
            
            // Build dynamic keyboard based on what's already set in the profile
            const user = profileData.user;
            const keyboard = [];
            
            // Only include options that make sense to update
    keyboard.push([Markup.button.callback("🔐 Change Password", "change_password")]);
            keyboard.push([Markup.button.callback("� Change Transaction PIN", "change_pin")]);
            
            // For other fields, only show if they're not set or if they're important enough to update
            if (!user.othername) {
                        keyboard.push([Markup.button.callback("📝 Add Other Name", "update_othername")]);
            }
            
            if (!user.address) {
                keyboard.push([Markup.button.callback("🏠 Add Address", "update_address")]);
            }
            
            // Only show options for fields that aren't already set
            if (!user.passport_photo) {
                keyboard.push([Markup.button.callback("📷 Upload Passport Photo", "upload_passport")]);
            }
            
            if (!user.bvn) {
                keyboard.push([Markup.button.callback("🆔 Upload BVN", "upload_bvn")]);
            }
            
            if (!user.proof_of_address_upload) {
                keyboard.push([Markup.button.callback("📄 Upload Proof of Address", "upload_proof_address")]);
            }
            
            keyboard.push([Markup.button.callback("« Back to Main Menu", "back_to_main")]);
            
            // Check if we already have an options message to update
            if (users[chatId].optionsMessageId) {
                try {
                    // Try to update existing options message
                    await ctx.telegram.editMessageText(
                        chatId,
                        users[chatId].optionsMessageId,
                        null,
                        "What would you like to update in your profile?",
                        { reply_markup: { inline_keyboard: keyboard } }
                    );
                } catch (error) {
                    console.error("Error updating options message:", error);
                    
                    // Try to delete the old message first
                    try {
                        await ctx.telegram.deleteMessage(chatId, users[chatId].optionsMessageId);
                    } catch (deleteError) {
                        console.error("Error deleting old options message:", deleteError);
                    }
                    
                    // Send a new message
                    const optionsMsg = await ctx.reply(
                        "What would you like to update in your profile?",
                        Markup.inlineKeyboard(keyboard)
                    );
                    users[chatId].optionsMessageId = optionsMsg.message_id;
                }
            } else {
                // Send new options message
                const optionsMsg = await ctx.reply(
                    "What would you like to update in your profile?",
                    Markup.inlineKeyboard(keyboard)
                );
                users[chatId].optionsMessageId = optionsMsg.message_id;
            }
            
            return true;
        }
    } else {
        await ctx.reply("⚠️ Please login to view your profile.");
        return false;
    }
};

// Profile view action
bot.action('view_profile', handleViewProfile);

// Profile update actions
bot.action('update_othername', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'awaiting_othername' };
    await ctx.reply("Please enter your other name:", {
        reply_markup: { force_reply: true, input_field_placeholder: "Enter your other name" }
    });
});

bot.action('update_address', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'awaiting_address' };
    await ctx.reply("Please enter your address:", {
        reply_markup: { force_reply: true, input_field_placeholder: "Enter your address" }
    });
});

bot.action('change_password', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'awaiting_current_password' };
    await ctx.reply("Please enter your current password:", {
        reply_markup: { force_reply: true, input_field_placeholder: "Enter your current password" }
    });
});

bot.action('change_pin', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'awaiting_current_pin' };
    await ctx.reply("Please enter your current transaction PIN:", {
        reply_markup: { force_reply: true, input_field_placeholder: "Enter your current PIN" }
    });
});

bot.action('upload_passport', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'awaiting_passport' };
    await ctx.reply(
        "Please upload your passport photograph as an image. " +
        "The image will be automatically converted to the required format.",
        { reply_markup: { force_reply: true } }
    );
});

bot.action('upload_bvn', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'awaiting_bvn' };
    await ctx.reply("Please enter your BVN number:", {
        reply_markup: { force_reply: true, input_field_placeholder: "Enter your 11-digit BVN" }
    });
});

bot.action('upload_proof_address', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'awaiting_proof_type' };
    await ctx.reply(
        "Please select the type of proof of address you want to upload:",
        Markup.inlineKeyboard([
            [Markup.button.callback("Utility Bill", "proof_utility")],
            [Markup.button.callback("Bank Statement", "proof_bank")],
            [Markup.button.callback("Government ID", "proof_govt")],
            [Markup.button.callback("« Cancel", "view_profile")]
        ])
    );
});

// Proof of address type selection
bot.action(/^proof_(.+)$/, async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    const proofType = ctx.match[1];
    
    // Map the button value to the API expected value
    const proofTypeMap = {
        'utility': '1',
        'bank': '2',
        'govt': '3'
    };
    
    users[chatId] = { 
        step: 'awaiting_proof_upload',
        proofType: proofTypeMap[proofType] 
    };
    
    await ctx.reply(
        "Please upload your proof of address document as an image. " +
        "The image will be automatically converted to the required format.",
        { reply_markup: { force_reply: true } }
    );
});

// Back to main menu
bot.action('back_to_main', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    const username = ctx.from.username || "User";
    
    // Check if user is authenticated
    const userData = getUser(chatId);
    if (userData && !isTokenExpired(userData.token_expires_at)) {
        // Check if profile is complete before showing main menu
        const profileData = await getUserProfile(ctx, chatId);
        if (profileData) {
            const requiredFields = [
                { key: 'address', name: 'Address' },
                { key: 'bvn', name: 'BVN' },
                { key: 'passport_url', name: 'Passport Photo' },
                { key: 'proof_of_address_url', name: 'Proof of Address' }
            ];
            
            const missingFields = requiredFields.filter(field => !profileData.user[field.key]);
            
            if (missingFields.length > 0) {
                // Profile is incomplete, start the mandatory profile setup
                await ctx.reply(
                    "🔐 *Profile Setup Required*\n\n" +
                    "To access all features, you need to complete your profile setup.",
                    { parse_mode: 'Markdown' }
                );
                
                // Show which fields are missing
                let message = "Please complete the following required information:\n\n";
                missingFields.forEach(field => {
                    message += `• ${field.name}\n`;
                });
                await ctx.reply(message);
                
                // Start the profile completion wizard automatically
                await ctx.reply(
                    "Let's complete your profile now:",
                    Markup.inlineKeyboard([
                        [Markup.button.callback("📝 Start Profile Setup", "complete_profile_wizard")]
                    ])
                );
            } else {
                // Profile is complete, show main menu
                await ctx.reply(
                    `What would you like to do today, ${username}?`,
                    getAuthenticatedMenu(username)
                );
            }
        } else {
            // Failed to get profile data, show main menu anyway
            await ctx.reply(
                `What would you like to do today, ${username}?`,
                getAuthenticatedMenu(username)
            );
        }
    } else {
        // User is not authenticated
        await ctx.reply(
            `Hello ${username}! You are not signed in.`,
            getUnauthenticatedMenu()
        );
    }
});

// Profile wizard logic (extracted for reuse)
async function continueProfileWizard(ctx, chatId) {
    // Fetch latest profile data
    const profileData = await getUserProfile(ctx, chatId);
    if (!profileData || !profileData.user) {
        await ctx.reply('❌ Unable to fetch your profile. Please try again later.');
        return;
    }
    // Determine missing fields
    const requiredFields = [
        { key: 'address', name: 'Address', step: 'awaiting_address', prompt: 'Please enter your address:', placeholder: 'Enter your address' },
        { key: 'bvn', name: 'BVN', step: 'awaiting_bvn', prompt: 'Please enter your BVN number:', placeholder: 'Enter your 11-digit BVN' },
        { key: 'passport_photo', name: 'Passport Photo', step: 'awaiting_passport', prompt: 'Please upload your passport photograph as an image.', placeholder: '' },
        { key: 'proof_of_address_upload', name: 'Proof of Address', step: 'awaiting_proof_type', prompt: 'Please select the type of proof of address you want to upload:', placeholder: '' },
        { key: 'othername', name: 'Other Name', step: 'awaiting_othername', prompt: 'Please enter your other name:', placeholder: 'Enter your other name' }
    ];
    const missing = requiredFields.filter(field => !profileData.user[field.key]);
    if (missing.length === 0) {
        await ctx.reply('✅ Your profile is already complete!');
        return;
    }
    // Start with the first missing field
    const next = missing[0];
    users[chatId] = users[chatId] || {};
    users[chatId].step = next.step;
    if (next.key === 'proof_of_address_url') {
        await ctx.reply(
            next.prompt,
            Markup.inlineKeyboard([
                [Markup.button.callback('Utility Bill', 'proof_utility')],
                [Markup.button.callback('Bank Statement', 'proof_bank')],
                [Markup.button.callback('Government ID', 'proof_govt')],
                [Markup.button.callback('« Cancel', 'view_profile')]
            ])
        );
    } else if (next.key === 'passport_url') {
        await ctx.reply(
            next.prompt + ' The image will be automatically converted to the required format.',
            { reply_markup: { force_reply: true } }
        );
    } else {
        await ctx.reply(next.prompt, {
            reply_markup: { force_reply: true, input_field_placeholder: next.placeholder }
        });
    }
}

// Start Profile Setup Wizard
bot.action('complete_profile_wizard', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    await continueProfileWizard(ctx, chatId);
});

// Register new user
bot.action('register', async (ctx) => {
    await ctx.answerCbQuery();
    const chatId = ctx.chat.id;
    users[chatId] = { step: 'register_firstname' };
    await ctx.reply('Please enter your first name:', {
        reply_markup: { force_reply: true, input_field_placeholder: 'First Name' }
    });
});

// Registration flow: handle user replies for registration steps
bot.on('text', async (ctx) => {
    const chatId = ctx.chat.id;
    const user = users[chatId];
    if (!user || !user.step) return;

    switch (user.step) {
        // Registration steps
        case 'register_firstname':
            user.firstname = ctx.message.text.trim();
            user.step = 'register_lastname';
            await ctx.reply('Please enter your last name:', {
                reply_markup: { force_reply: true, input_field_placeholder: 'Last Name' }
            });
            break;
        case 'register_lastname':
            user.lastname = ctx.message.text.trim();
            user.step = 'register_email';
            await ctx.reply('Please enter your email address:', {
                reply_markup: { force_reply: true, input_field_placeholder: 'Email' }
            });
            break;
        case 'register_email':
            user.email = ctx.message.text.trim();
            user.step = 'register_password';
            await ctx.reply('Please enter a password:', {
                reply_markup: { force_reply: true, input_field_placeholder: 'Password' }
            });
            break;
        case 'register_password':
            user.password = ctx.message.text.trim();
            // Call your registerUser API here
            try {
                const result = await registerUser(user.firstname, user.lastname, user.email, user.password);
                if (result && result.success) {
                    await ctx.reply('✅ Registration successful! Please check your email to verify your account.');
                } else {
                    await ctx.reply('❌ Registration failed. ' + (result && result.message ? result.message : 'Please try again.'));
                }
            } catch (err) {
                await ctx.reply('❌ Registration error. Please try again later.');
            }
            delete users[chatId];
            break;
        // Profile setup steps
        case 'awaiting_address':
            user.address = ctx.message.text.trim();
            try {
                const result = await updateAddress(ctx, chatId, user.address);
                if (result && result.success) {
                    await ctx.reply('✅ Address updated!');
                } else {
                    await ctx.reply('❌ Failed to update address. ' + (result && result.message ? result.message : 'Please try again.'));
                }
            } catch (err) {
                await ctx.reply('❌ Error updating address. Please try again later.');
            }
            // Continue to next missing field by calling the wizard function directly
            await continueProfileWizard(ctx, chatId);
            break;
        case 'awaiting_bvn':
            user.bvn = ctx.message.text.trim();
            try {
                const result = await uploadBVN(ctx, chatId, user.bvn);
                if (result) {
                    await ctx.reply('✅ BVN updated!');
                } else {
                    await ctx.reply('❌ Failed to update BVN. Please try again.');
                }
            } catch (err) {
                await ctx.reply('❌ Error updating BVN. Please try again later.');
            }
            // Continue to next missing field by calling the wizard function directly
            await continueProfileWizard(ctx, chatId);
            break;
        case 'awaiting_passport':
            await ctx.reply('📷 Please send your passport photo as an image (not text).');
            break;
        default:
            break;
    }
});

// Launch the bot
bot.launch();

// Graceful stop
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));